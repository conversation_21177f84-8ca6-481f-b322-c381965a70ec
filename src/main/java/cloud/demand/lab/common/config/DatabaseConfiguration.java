package cloud.demand.lab.common.config;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.enums.FeatureEnum;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import yunti.boot.data.Database;

@Configuration
public class DatabaseConfiguration {

    /**
     * cloud_demand数据库，存放CRP云需求管理数据
     */
    @Bean("demandDBHelper")
    @Lazy
    DBHelper demandDBHelper(@Database("demand") JdbcTemplate demandJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(demandJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // 云梯mysql数据库
    @Bean("yuntiDBHelper")
    DBHelper yuntiDBHelper(@Database("yunti") JdbcTemplate yuntiJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(yuntiJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // =========================== 原星云资源中台数据库：rrp
    @Bean("rrpDBHelper")
    DBHelper rrpDBHelper(@Database("rrp") JdbcTemplate rrpJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(rrpJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    @Bean("cdCommonDbHelper")
    DBHelper cdCommonDbHelper(@Database("cdcommon") JdbcTemplate demandJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(demandJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("cdCommonDbHelper");
        return dbHelper;
    }

    @Bean("cdLabDbHelper")
    DBHelper cdLabDbHelper(@Database("cdlab") JdbcTemplate demandJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(demandJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    /**
     * 科天提供的腾讯云库存信息
     */
    @Bean("planDBHelper")
    DBHelper planDBHelper(@Database("plan") JdbcTemplate planJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(planJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    @Bean("obsDBHelper")
    DBHelper obsDBHelper(@Database("obsdb") JdbcTemplate obsJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(obsJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ========================== 原提供给mck的数据库，现在给云产品cvm放数据
    @Bean("cvmmetricDBHelper")
    DBHelper cvmmetricDBHelper(@Database("cvmmetric") JdbcTemplate jdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(jdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ========================== erp resource数据库
    @Bean("resourcedbDBHelper")
    DBHelper resourcedbDBHelper(@Database("resourcedb") JdbcTemplate resourcedbJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(resourcedbJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ========================== erp res_plan需求预测数据库
    @Bean("resplanDBHelper")
    DBHelper resplanDBHelper(@Database("resplan") JdbcTemplate resplanJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(resplanJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }


    /* crp 新ck cloud_demand 库 */
    @ConditionalOnProperty(value = "yunti.jdbc.ckcld.enabled", matchIfMissing = false)
    @Bean("ckcldDBHelper")
    DBHelper ckcldDBHelper(@Database("ckcld") JdbcTemplate ckcldTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(ckcldTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckcldDbHelper");
        return dbHelper;
    }


    @ConditionalOnProperty(value = "yunti.jdbc.ckstdcrp.enabled", matchIfMissing = false)
    @Bean("ckstdcrpDBHelper")
    DBHelper ckstdcrpDBHelper(@Database("ckstdcrp") JdbcTemplate ckcldStdCrpTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(ckcldStdCrpTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckstdcrpDbHelper");
        return dbHelper;
    }

    @ConditionalOnProperty(value = "yunti.jdbc.ckcldstdcrpswap.enabled", matchIfMissing = false)
    @Bean("ckcldStdCrpSwapDBHelper")
    DBHelper ckcldStdCrpSwapDBHelper(@Database("ckcldstdcrpswap") JdbcTemplate ckcldStdCrpSwapTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(ckcldStdCrpSwapTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckcldStdCrpSwapDBHelper");
        return dbHelper;
    }


    @Bean("ckcubesDBHelper")
    DBHelper ckcubesDBHelper(@Database("ckcubes") JdbcTemplate ckcubesTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(ckcubesTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckcubesDbHelper");
        return dbHelper;
    }


    /**
     * crp 模型预测ck库，因为测试环境和生产ck版本不一致而临时搞的
     */
    @ConditionalOnProperty(value = "yunti.jdbc.ckforecaststdcrp.enabled", matchIfMissing = false)
    @Bean("ckForecastStdCrpDBHelper")
    DBHelper ckForecastStdCrpDBHelper(@Database("ckforecaststdcrp") JdbcTemplate ckForecastStdCrpTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(ckForecastStdCrpTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckForecastStdCrpDBHelper");
        return dbHelper;
    }

    @ConditionalOnProperty(value = "yunti.jdbc.ckforecastswapstdcrp.enabled", matchIfMissing = false)
    @Bean("ckForecastStdCrpSwapDBHelper")
    DBHelper ckForecastStdCrpSwapDBHelper(@Database("ckforecastswapstdcrp") JdbcTemplate ckForecastStdCrpTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(ckForecastStdCrpTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckForecastStdCrpSwapDBHelper");
        return dbHelper;
    }

    @ConditionalOnProperty(value = "yunti.jdbc.ckcldstdcrp.enabled", matchIfMissing = false)
    @Bean("ckcldStdCrpDBHelper")
    DBHelper ckcldStdCrpDBHelper(@Database("ckcldstdcrp") JdbcTemplate ckcldStdCrpTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(ckcldStdCrpTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("ckcldStdCrpDBHelper");
        return dbHelper;
    }

    // ========================== crs产品的数据源
    @Bean("crsDBHelper")
    DBHelper crsDBHelper(@Database("crsdb") JdbcTemplate plancdbJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(plancdbJdbcTemplate);
        dbHelper.setDbHelperName("crsdbHelper");
        return new SpringJdbcDBHelper(plancdbJdbcTemplate);
    }

    // ========================== ERP备库

    @Bean("erpBakDBHelper")
    DBHelper erpBakDBHelper(@Database("erpbak") JdbcTemplate demandJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(demandJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("erpBakDBHelper");
        return dbHelper;
    }

    @Bean("purchasereportDBHelper")
    DBHelper purchasereportDBHelper(@Database("purchasereport") JdbcTemplate purchasereportJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(purchasereportJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    @Bean("shuttleDBHelper")
    DBHelper shuttleDBHelper(@Database("shuttle") JdbcTemplate shuttleJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(shuttleJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ========================== plan的端到端数据源 - cbs
    @Bean("plancbsDBHelper")
    DBHelper plancbsDBHelper(@Database("plancbs") JdbcTemplate plancbsJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setJdbcTemplate(plancbsJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        return dbHelper;
    }

    @Bean("planDbDBHelper")
    DBHelper planDbDBHelper(@Database("plandb") JdbcTemplate plandbJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setJdbcTemplate(plandbJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        return dbHelper;
    }

    @Bean("newckdemandDBHelper")
    @Deprecated
    DBHelper newckdemandDBHelper(@Database("newckdemand") JdbcTemplate newckdemandTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(newckdemandTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }


    @Bean("ckyuntiDBHelper")
    @Deprecated
    DBHelper ckyuntiDBHelper(@Database("ckyunti") JdbcTemplate ckyuntiTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setJdbcTemplate(ckyuntiTemplate);
        return dbHelper;
    }


    /**
     * clickhouse crp标准宽表
     */
    @ConditionalOnProperty(value = "yunti.jdbc.readonlyckstdcrp.enabled", matchIfMissing = false)
    @Bean("prodReadOnlyCkStdCrpDBHelper")
    DBHelper prodReadOnlyCkStdCrpDBHelper(@Database("readonlyckstdcrp") JdbcTemplate readonlyckstdcrpTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(readonlyckstdcrpTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("prodReadOnlyCkStdCrpDBHelper");
        return dbHelper;
    }

    @Bean("arch2DBHelper")
    DBHelper arch2DBHelper(@Database("arch2") JdbcTemplate arch2Template) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(arch2Template);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // ============================= plan-report
    @Bean("planReportDBHelper")
    DBHelper planReportDBHelper(@Database("planreportdb") JdbcTemplate planReportTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(planReportTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("planReportDBHelper");
        return dbHelper;
    }


    // ============================= gpu
    @Bean("gpuDBHelper")
    DBHelper gpuDBHelper(@Database("gpu") JdbcTemplate planReportTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(planReportTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("gpuDBHelper");
        return dbHelper;
    }

    // =========================== cbs服务水平库
    @Bean("cbshawkeyeDBHelper")
    DBHelper cbshawkeyeDBHelper(@Database("cbshawkeye") JdbcTemplate planReportTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper(planReportTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        dbHelper.setDbHelperName("cbshawkeyeDBHelper");
        return dbHelper;
    }

    // ========================== 云梯的需求预测和退回计划数据库 yunti_demand
    @Bean("yuntidemandDBHelper")
    DBHelper yuntidemandDBHelper(@Database("yuntidemand") JdbcTemplate yuntidemandJdbcTemplate) {
        return new SpringJdbcDBHelper(yuntidemandJdbcTemplate);
    }

    @Bean("dialsystemDBHelper")
    DBHelper dialsystemDBHelper(@Database("dialsystem") JdbcTemplate arch2Template) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(arch2Template);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    @Bean("tdsqlresourceDBHelper")
    DBHelper tdsqlresourceDBHelper(@Database("tdsqlresource") JdbcTemplate arch2Template) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(arch2Template);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

    // plan cos数据库
    @Bean("planCosDbHelper")
    DBHelper planCosDbHelper(@Database("plancos") JdbcTemplate plancosJdbcTemplate) {
        SpringJdbcDBHelper dbHelper = new SpringJdbcDBHelper();
        dbHelper.turnOnFeature(FeatureEnum.LAZY_DETECT_DATABASE_TYPE);
        dbHelper.setJdbcTemplate(plancosJdbcTemplate);
        dbHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        return dbHelper;
    }

}
