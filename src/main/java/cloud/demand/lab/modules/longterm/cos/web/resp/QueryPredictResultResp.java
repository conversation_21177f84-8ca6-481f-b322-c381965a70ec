package cloud.demand.lab.modules.longterm.cos.web.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class QueryPredictResultResp {

    /**
     * 预测结果信息，以线数据为单位
     */
    private List<Line> lines;

    @Data
    public static class Line {
        /**
         * 范围：全部、内部、外部 3种
         */
        private String scope;
        /**
         * 数据类型：HISTORY、PREDICT 2种，表示历史数据还是预测数据
         */
        private String type;
        /**
         * 当数据是PREDICT时，表示预测所用的算法
         */
        private String algorithm;

        private List<Point> points;

        /**当数据是PREDICT时，算法的相关信息*/
        private Map<String, Object> algorithmParams;

        private List<IncreaseInfo> increaseInfos;
    }

    @Data
    public static class Point {
        /**日期*/
        private String date;
        /**数值*/
        private BigDecimal value;
    }

    /**增速信息*/
    @Data
    public static class IncreaseInfo {
        /**日期名称*/
        private String dateName;
        /**开始日期*/
        private String startDate;
        /**结束日期*/
        private String endDate;
        /**增速，注意不是百分比，0.14则表示14%*/
        private BigDecimal rate;
    }

}
